<template>
  <el-container class="layout-container">
    <el-container class="main-container">
      <el-header class="header">
        <app-header />
      </el-header>
      <dashboard-main />
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import AppHeader from '../components/layout/AppHeader.vue'
import AppSidebar from '../components/layout/AppSidebar.vue'
import DashboardMain from '../components/dashboard/DashboardMain.vue'
</script>

<style scoped>
.layout-container {
  height: 100vh;
  width: 100%;
  background-color: var(--color-background-soft);
  overflow: hidden;
}

.sidebar {
  background-color: var(--color-background-elevated);
  color: var(--color-text);
  transition: all 0.3s ease;
}

.main-container {
  background-color: var(--color-background-soft);
  flex: 1;
  overflow: hidden;
}

.header {
  background-color: var(--color-background-elevated);
  padding: 0;
  height: 64px;
  box-shadow: var(--shadow-sm);
  z-index: 10;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .layout-container {
    flex-direction: column;
  }

  .sidebar {
    width: 100% !important;
    height: auto;
    order: 2;
  }

  .main-container {
    order: 1;
    height: calc(100vh - 200px);
  }
}

@media (max-width: 768px) {
  .layout-container {
    height: auto;
    min-height: 100vh;
  }

  .sidebar {
    position: fixed;
    top: 64px;
    left: -280px;
    height: calc(100vh - 64px);
    width: 280px !important;
    z-index: 1000;
    transition: left 0.3s ease;
  }

  .sidebar.mobile-open {
    left: 0;
  }

  .main-container {
    width: 100%;
    height: calc(100vh - 64px);
    margin-top: 0;
  }

  .header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1001;
  }
}

@media (max-width: 480px) {
  .header {
    height: 56px;
  }

  .sidebar {
    top: 56px;
    height: calc(100vh - 56px);
    width: 100% !important;
  }

  .main-container {
    height: calc(100vh - 56px);
  }
}
</style>
