<template>
  <el-header class="app-header">
    <div class="header-left">
      <div class="brand">
        <el-icon class="brand-icon"><Monitor /></el-icon>
        <h1 class="title">IoT设备监控中心</h1>
      </div>

      <!-- 导航菜单 -->
      <el-menu
        mode="horizontal"
        :default-active="activeIndex"
        class="header-menu"
        @select="handleSelect"
      >
        <el-menu-item index="/">
          <el-icon><Monitor /></el-icon>
          <span>监控中心</span>
        </el-menu-item>
        <el-menu-item index="/factory-map">
          <el-icon><Location /></el-icon>
          <span>工厂地图</span>
        </el-menu-item>
      </el-menu>
    </div>
    <div class="header-right">
      <el-badge :value="3" class="notification">
        <el-button type="text" class="notification-btn">
          <el-icon><Bell /></el-icon>
        </el-button>
      </el-badge>
      <el-dropdown class="user-dropdown">
        <div class="user-info">
          <el-avatar :size="32" class="user-avatar">
            <el-icon><User /></el-icon>
          </el-avatar>
          <span class="user-name">管理员</span>
          <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item>
              <el-icon><Setting /></el-icon>
              个人设置
            </el-dropdown-item>
            <el-dropdown-item divided>
              <el-icon><SwitchButton /></el-icon>
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </el-header>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Bell, Monitor, Location, User, ArrowDown, Setting, SwitchButton } from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()

const isDark = ref(false)

// 当前激活的菜单项
const activeIndex = computed(() => route.path)

const toggleTheme = (val: boolean) => {
  // 实现主题切换逻辑
}

const handleSelect = (key: string) => {
  router.push(key)
}
</script>


<style scoped>
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  height: 64px;
  background-color: var(--color-background-elevated);
  border-bottom: 1px solid var(--color-border);
  box-shadow: var(--shadow-sm);
  backdrop-filter: blur(8px);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 32px;
}

.brand {
  display: flex;
  align-items: center;
  gap: 12px;
}

.brand-icon {
  font-size: 24px;
  color: var(--color-primary);
}

.title {
  font-size: 20px;
  font-weight: 600;
  color: var(--color-text);
  margin: 0;
  letter-spacing: -0.025em;
}

.header-menu {
  background: transparent;
  border: none;
}

.header-menu :deep(.el-menu-item) {
  color: var(--color-text-secondary);
  border-bottom: 2px solid transparent;
  height: 64px;
  line-height: 64px;
  padding: 0 16px;
  margin: 0 4px;
  border-radius: var(--border-radius-sm);
  transition: all 0.3s ease;
  font-weight: 500;
}

.header-menu :deep(.el-menu-item:hover) {
  background-color: var(--color-surface-hover);
  color: var(--color-primary);
  transform: translateY(-1px);
}

.header-menu :deep(.el-menu-item.is-active) {
  color: var(--color-primary);
  border-bottom-color: var(--color-primary);
  background-color: var(--color-primary-light);
  font-weight: 600;
}

.header-menu :deep(.el-menu-item .el-icon) {
  margin-right: 8px;
  font-size: 16px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.notification {
  position: relative;
}

.notification-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  transition: all 0.3s ease;
}

.notification-btn:hover {
  background-color: var(--color-surface-hover);
  color: var(--color-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.user-dropdown {
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
  color: var(--color-text);
}

.user-info:hover {
  background-color: var(--color-surface-hover);
  transform: translateY(-1px);
}

.user-avatar {
  background-color: var(--color-primary-light);
  color: var(--color-primary);
  border: 2px solid var(--color-border);
}

.user-name {
  font-weight: 500;
  font-size: 14px;
}

.dropdown-icon {
  font-size: 12px;
  color: var(--color-text-tertiary);
  transition: transform 0.3s ease;
}

.user-dropdown.is-active .dropdown-icon {
  transform: rotate(180deg);
}

/* Responsive design */
@media (max-width: 768px) {
  .app-header {
    padding: 0 16px;
  }

  .header-left {
    gap: 16px;
  }

  .title {
    font-size: 18px;
  }

  .user-name {
    display: none;
  }
}
</style>