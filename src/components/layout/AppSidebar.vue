<template>
  <el-aside width="280px" class="app-sidebar">
    <div class="sidebar-header">
      <h3 class="sidebar-title">设备管理</h3>
    </div>

    <div class="sidebar-content">
      <el-input
        v-model="searchKey"
        placeholder="搜索设备..."
        prefix-icon="Search"
        clearable
        class="search-input"
      />

      <el-button type="primary" class="history-btn">
        <el-icon><Clock /></el-icon>
        查询历史数据
      </el-button>

      <div class="filter-section">
        <h4 class="filter-title">设备状态</h4>
        <el-radio-group v-model="deviceStatus" class="status-filter">
          <el-radio-button label="all">全部</el-radio-button>
          <el-radio-button label="online">在线</el-radio-button>
          <el-radio-button label="offline">离线</el-radio-button>
          <el-radio-button label="error">异常</el-radio-button>
        </el-radio-group>
      </div>

      <div class="tree-section">
        <h4 class="tree-title">设备列表</h4>
        <el-tree
          :data="deviceTree"
          :props="defaultProps"
          :filter-node-method="filterNode"
          class="device-tree"
          show-checkbox
          node-key="id"
        />
      </div>
    </div>
  </el-aside>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Clock } from '@element-plus/icons-vue'

const searchKey = ref('')
const deviceStatus = ref('all')

const deviceTree = ref([
  {
    id: 1,
    label: '生产线设备',
    children: [
      { id: 11, label: '设备A-001', status: 'online' },
      { id: 12, label: '设备A-002', status: 'offline' },
      { id: 13, label: '设备A-003', status: 'warning' }
    ]
  },
  {
    id: 2,
    label: '仓储设备',
    children: [
      { id: 21, label: '仓储B-001', status: 'online' },
      { id: 22, label: '仓储B-002', status: 'error' }
    ]
  },
  {
    id: 3,
    label: '检测设备',
    children: [
      { id: 31, label: '检测C-001', status: 'online' },
      { id: 32, label: '检测C-002', status: 'online' },
      { id: 33, label: '检测C-003', status: 'warning' }
    ]
  }
])

const defaultProps = {
  children: 'children',
  label: 'label'
}

const filterNode = (value: string, data: any) => {
  if (!value) return true
  return data.label.toLowerCase().includes(value.toLowerCase())
}

watch(searchKey, (val) => {
  // TODO: 实现搜索逻辑
  console.log('搜索关键词:', val)
})

watch(deviceStatus, (val) => {
  // TODO: 实现状态过滤逻辑
  console.log('过滤状态:', val)
})
</script>

<style scoped>
.app-sidebar {
  background-color: var(--color-background-elevated);
  border-right: 1px solid var(--color-border);
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.sidebar-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid var(--color-border);
}

.sidebar-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text);
  margin: 0;
  letter-spacing: -0.025em;
}

.sidebar-content {
  flex: 1;
  padding: 20px 24px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow: hidden;
}

.search-input {
  width: 100%;
}

.search-input :deep(.el-input__wrapper) {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
}

.search-input :deep(.el-input__wrapper:hover) {
  border-color: var(--color-border-hover);
}

.search-input :deep(.el-input__wrapper.is-focus) {
  border-color: var(--color-border-focus);
  box-shadow: 0 0 0 3px var(--color-primary-light);
}

.search-input :deep(.el-input__inner) {
  color: var(--color-text);
}

.search-input :deep(.el-input__inner::placeholder) {
  color: var(--color-text-tertiary);
}

.history-btn {
  width: 100%;
  height: 40px;
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  border-radius: var(--border-radius);
  font-weight: 500;
  transition: all 0.3s ease;
}

.history-btn:hover {
  background-color: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.history-btn :deep(.el-icon) {
  margin-right: 8px;
}

.filter-section,
.tree-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.filter-title,
.tree-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text-secondary);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-filter {
  width: 100%;
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  padding: 4px;
}

.status-filter :deep(.el-radio-button) {
  flex: 1;
}

.status-filter :deep(.el-radio-button__inner) {
  width: 100%;
  background-color: transparent;
  border: none;
  color: var(--color-text-secondary);
  font-size: 12px;
  font-weight: 500;
  padding: 8px 12px;
  border-radius: var(--border-radius-sm);
  transition: all 0.3s ease;
}

.status-filter :deep(.el-radio-button__inner:hover) {
  background-color: var(--color-surface-hover);
  color: var(--color-text);
}

.status-filter :deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
  background-color: var(--color-primary);
  color: white;
  box-shadow: var(--shadow-sm);
  font-weight: 600;
}

.tree-section {
  flex: 1;
  overflow: hidden;
}

.device-tree {
  flex: 1;
  overflow-y: auto;
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  padding: 12px;
}

.device-tree :deep(.el-tree-node__content) {
  background-color: transparent;
  color: var(--color-text);
  height: 36px;
  border-radius: var(--border-radius-sm);
  margin-bottom: 2px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.device-tree :deep(.el-tree-node__content:hover) {
  background-color: var(--color-surface-hover);
  transform: translateX(4px);
}

.device-tree :deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: var(--color-primary-light);
  color: var(--color-primary);
  font-weight: 600;
}

.device-tree :deep(.el-tree-node__expand-icon) {
  color: var(--color-text-tertiary);
}

.device-tree :deep(.el-checkbox) {
  margin-right: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-sidebar {
    width: 240px !important;
  }

  .sidebar-content {
    padding: 16px 20px;
    gap: 16px;
  }

  .sidebar-title {
    font-size: 16px;
  }
}
</style>