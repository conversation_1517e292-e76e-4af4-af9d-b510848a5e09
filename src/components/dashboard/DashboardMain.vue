<template>
  <el-main class="dashboard-main">
    <!-- 控制栏 -->
    <div class="control-bar">
      <div class="control-left">
        <h3 class="dashboard-title">实时监控中心</h3>
        <p class="dashboard-subtitle">设备状态实时监控与数据分析</p>
      </div>
      <div class="control-right">
        <el-select v-model="timeRange" class="time-range" placeholder="选择时间范围">
          <el-option label="最近1小时" value="1h" />
          <el-option label="最近24小时" value="24h" />
          <el-option label="最近7天" value="7d" />
          <el-option label="自定义" value="custom" />
        </el-select>
        <el-button type="primary" @click="showHistoryPanel = true" class="history-query-btn">
          <el-icon><Clock /></el-icon>
          历史数据查询
        </el-button>
        <el-button type="default" class="refresh-btn">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <el-row :gutter="20">
      <!-- 左侧核心指标 -->
      <el-col :span="6">
        <el-card style="margin-bottom: 10px;" class="metrics-panel">
          <template #header>
            <div class="panel-header">核心指标</div>
          </template>
          <el-row :gutter="20" class="metrics-grid">
            <el-col :span="12" v-for="metric in coreMetricStore.boardList" :key="metric.name">
              <div class="metric-item">
                <div class="metric-header">
                  {{ metric.name }}
                </div>
                <div class="metric-value" :class="metric.status">{{ metric.value }}</div>
              </div>
            </el-col>
          </el-row>
        </el-card>
        <el-card class="chart-card metrics-panel"
        @click="openHistory('device_type')">
          <template #header>
            <div class="panel-header">设备类型分布</div>
          </template>
          <!-- <BaseChart
            :options=" "
            style="height: 220px;"
          /> -->
        </el-card>
      </el-col>

      <!-- 中间地图区域 -->
      <el-col :span="12">
        <el-card class="map-card panel-header">
          <template #header>
            <div class="panel-header">工厂车间地图</div>
          </template>
          <FactoryMap></FactoryMap>
        </el-card>
      </el-col>

      <!-- 右侧图表 -->
      <el-col :span="6" >
        <el-card class="chart-card panel-header" style="margin-bottom: 10px;">
          <template #header>实时环境温度</template>
           <BaseChart
            :options="environmentDataChartOptions"
            style="height: 220px;"
          />
        </el-card>
        <el-card class="chart-card" @click="openHistory('request_count')">
          <template #header>
            <div class="panel-header">实时通信数据</div>
          </template>
          <BaseChart
            :options="requestCountChartOptions"
            style="height: 220px;"
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 添加历史数据弹窗 -->
    <HistoryDataDialog
      v-model="showHistory"
      :type="currentType"
    />

    <!-- 历史数据查询面板弹窗 -->
    <el-dialog
      v-model="showHistoryPanel"
      title="历史数据查询"
      width="90%"
      :before-close="handleCloseHistoryPanel"
    >
      <HistoryDataPanel />
    </el-dialog>
  </el-main>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { Clock, Refresh } from '@element-plus/icons-vue'
import BaseChart from '../charts/BaseChart.vue'
import HistoryDataPanel from './HistoryDataPanel.vue'
import { createLineChart, createBarChart, createPieChart, createMapChart } from '../../utils/chartOptions'
import { useCoreMetricStore } from '../../stores/CoreMetricData'
import { useEnvironmentDataStore } from '../../stores/EnvironmentData'
import { useDeviceTelemetryDataStore } from '../../stores/DeviceTelemetryData'
import { useRealtimeStore } from '../../stores/realtime'
import FactoryMap from '../FactoryMap.vue'
const realtimeStore = useRealtimeStore()  // 实时监控
const coreMetricStore = useCoreMetricStore()   // 核心指标
const environmentDataStore = useEnvironmentDataStore()  //环境数据
const telemetryData=useDeviceTelemetryDataStore()  //通信数据
const timeRange = ref('1h')
const fl = ref(false)
const showHistoryPanel = ref(false)


//环境数据统计图表配置
const environmentDataChartOptions = computed(() => {
  const boardList = environmentDataStore.boardList;
  // 确保数据按时间戳排序
  const sortedData = [...boardList].sort((a, b) => a.timestamp - b.timestamp);
  // 只取最新的10条数据
  const recentData = sortedData.slice(-10);

  return createLineChart({
    series: recentData.map(item => item.value),
    xAxis: recentData.map(item => new Date(item.timestamp).toLocaleTimeString())
  });
})

// 请求量统计图表配置
const requestCountChartOptions = computed(() => createBarChart({
  series: telemetryData.boardData.map(item => item.value),
  xAxis: {
    type:'category',
    data: telemetryData.boardData.map(item => new Date(item.timestamp).toLocaleTimeString())},
  maxPoints: 10  // 限制显示最新的20条数据
}))


// 注册地图+读取数据
onMounted(() => {
  // 启动实时监控
  realtimeStore.setMonitoring(true)
})

const showHistory = ref(false)
const currentType = ref('')

const openHistory = (type: string) => {
  currentType.value = type
  showHistory.value = true
}

const handleCloseHistoryPanel = () => {
  showHistoryPanel.value = false
}
</script>

<style scoped>
.dashboard-main {
  padding: 24px;
  background-color: var(--color-background-soft);
  min-height: 100vh;
  color: var(--color-text);
}

/* 控制栏样式 */
.control-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  margin-bottom: 12px;
  border-bottom: 1px solid var(--color-border);
}

.control-left {
  flex: 1;
}

.dashboard-title {
  font-weight: 700;
  color: var(--color-text);
  margin: 0 0 8px 0;
  letter-spacing: -0.025em;
}

.dashboard-subtitle {
  font-size: 16px;
  color: var(--color-text-secondary);
  margin: 0;
  font-weight: 400;
}

.control-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.time-range {
  width: 160px;
}

.time-range :deep(.el-input__wrapper) {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
}

.time-range :deep(.el-input__wrapper:hover) {
  border-color: var(--color-border-hover);
}

.time-range :deep(.el-input__wrapper.is-focus) {
  border-color: var(--color-border-focus);
  box-shadow: 0 0 0 3px var(--color-primary-light);
}

.history-query-btn,
.refresh-btn {
  height: 40px;
  border-radius: var(--border-radius);
  font-weight: 500;
  transition: all 0.3s ease;
}

.history-query-btn {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.history-query-btn:hover {
  background-color: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.refresh-btn {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
}

.refresh-btn:hover {
  background-color: var(--color-surface-hover);
  border-color: var(--color-border-hover);
  color: var(--color-text);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.history-query-btn :deep(.el-icon),
.refresh-btn :deep(.el-icon) {
  margin-right: 6px;
}

/* 卡片通用样式 */
:deep(.el-card) {
  background-color: var(--color-background-elevated);
  border: 1px solid var(--color-border);
  box-shadow: var(--shadow);
  border-radius: var(--border-radius-lg);
  transition: all 0.3s ease;
  overflow: hidden;
}

:deep(.el-card:hover) {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
  border-color: var(--color-border-hover);
}

:deep(.el-card__header) {
  background-color: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  padding: 16px 20px;
  font-weight: 600;
  color: var(--color-text);
}

:deep(.el-card__body) {
  padding: 20px;
}


/* 指标卡片样式 */
.metrics-panel {
  height: 300px;
  margin-bottom: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.metrics-panel:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.metrics-grid {
  padding: 0;
  gap: 16px;
}

.metric-item {
  background: linear-gradient(135deg, var(--color-surface) 0%, var(--color-background-mute) 100%);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  padding: 16px;
  margin-bottom: 16px;
  transition: all 0.3s ease;
  height: 100px;
  position: relative;
  overflow: hidden;
}

.metric-item:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.metric-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-primary-hover));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.metric-item:hover::before {
  opacity: 1;
}

.metric-header {
  color: var(--color-text-secondary);
  font-size: 13px;
  font-weight: 500;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.metric-value {
  color: var(--color-text);
  font-size: 24px;
  font-weight: 700;
  text-align: center;
  margin-top: 8px;
}

/* 状态颜色 */
.metric-value.normal {
  color: var(--color-success);
}

.metric-value.warning {
  color: var(--color-warning);
}

.metric-value.error {
  color: var(--color-error);
}

/* 图表容器样式 */
.chart-card {
  height: 300px;
  position: relative;
}

.chart-card :deep(.el-card__body) {
  padding: 16px;
  height: calc(100% - 60px);
}

/* 地图卡片特殊样式 */
.map-card {
  height: 610px;
  background: var(--color-background-elevated);
}

/* 面板标题样式 */
.panel-header {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text);
  letter-spacing: -0.025em;
  margin: 0;
}

/* 加载状态样式 */
.loading-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  color: var(--color-text-tertiary);
  background-color: var(--color-surface);
  border-radius: var(--border-radius);
}

/* 图表占位符样式 */
.chart-placeholder {
  height: 250px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-surface);
  border: 2px dashed var(--color-border);
  border-radius: var(--border-radius);
  color: var(--color-text-tertiary);
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .dashboard-main {
    padding: 16px;
  }

  .control-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    padding: 20px 0;
  }

  .control-right {
    width: 100%;
    justify-content: flex-start;
  }

  .dashboard-title {
    font-size: 24px;
  }

  .dashboard-subtitle {
    font-size: 14px;
  }
}

@media (max-width: 768px) {
  .dashboard-main {
    padding: 12px;
  }

  .control-right {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .time-range {
    width: 100%;
  }

  .history-query-btn,
  .refresh-btn {
    width: 100%;
    justify-content: center;
  }

  .dashboard-title {
    font-size: 20px;
  }

  .map-card {
    height: 400px;
  }

  .chart-card {
    height: 250px;
  }

  .metrics-panel {
    height: auto;
  }

  .metric-item {
    height: 80px;
    padding: 12px;
  }

  .metric-value {
    font-size: 20px;
  }
}
</style>
