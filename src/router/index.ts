import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/factory-map',
      name: 'factory-map',
      component: () => import('../views/FactoryMapView.vue'),
    },
    // 重定向未知路由到首页
    {
      path: '/:pathMatch(.*)*',
      redirect: '/'
    }
  ],
})

export default router
