/* Modern Light Theme Color Palette */
:root {
  /* Base Colors */
  --vt-c-white: #ffffff;
  --vt-c-white-soft: #fafbfc;
  --vt-c-white-mute: #f5f7fa;
  --vt-c-gray-50: #f9fafb;
  --vt-c-gray-100: #f3f4f6;
  --vt-c-gray-200: #e5e7eb;
  --vt-c-gray-300: #d1d5db;
  --vt-c-gray-400: #9ca3af;
  --vt-c-gray-500: #6b7280;
  --vt-c-gray-600: #4b5563;
  --vt-c-gray-700: #374151;
  --vt-c-gray-800: #1f2937;
  --vt-c-gray-900: #111827;

  /* Primary Colors - Modern Blue */
  --vt-c-primary-50: #eff6ff;
  --vt-c-primary-100: #dbeafe;
  --vt-c-primary-200: #bfdbfe;
  --vt-c-primary-300: #93c5fd;
  --vt-c-primary-400: #60a5fa;
  --vt-c-primary-500: #3b82f6;
  --vt-c-primary-600: #2563eb;
  --vt-c-primary-700: #1d4ed8;
  --vt-c-primary-800: #1e40af;
  --vt-c-primary-900: #1e3a8a;

  /* Success Colors */
  --vt-c-success-50: #f0fdf4;
  --vt-c-success-500: #22c55e;
  --vt-c-success-600: #16a34a;

  /* Warning Colors */
  --vt-c-warning-50: #fffbeb;
  --vt-c-warning-500: #f59e0b;
  --vt-c-warning-600: #d97706;

  /* Error Colors */
  --vt-c-error-50: #fef2f2;
  --vt-c-error-500: #ef4444;
  --vt-c-error-600: #dc2626;

  /* Dividers */
  --vt-c-divider-light-1: rgba(0, 0, 0, 0.12);
  --vt-c-divider-light-2: rgba(0, 0, 0, 0.06);
  --vt-c-border: var(--vt-c-gray-200);
  --vt-c-border-hover: var(--vt-c-gray-300);

  /* Text Colors */
  --vt-c-text-primary: var(--vt-c-gray-900);
  --vt-c-text-secondary: var(--vt-c-gray-600);
  --vt-c-text-tertiary: var(--vt-c-gray-500);
  --vt-c-text-disabled: var(--vt-c-gray-400);
}

/* Semantic color variables for IoT Dashboard */
:root {
  /* Background Colors */
  --color-background: var(--vt-c-white);
  --color-background-soft: var(--vt-c-gray-50);
  --color-background-mute: var(--vt-c-gray-100);
  --color-background-elevated: var(--vt-c-white);

  /* Surface Colors */
  --color-surface: var(--vt-c-white);
  --color-surface-hover: var(--vt-c-gray-50);
  --color-surface-active: var(--vt-c-gray-100);

  /* Border Colors */
  --color-border: var(--vt-c-border);
  --color-border-hover: var(--vt-c-border-hover);
  --color-border-focus: var(--vt-c-primary-500);

  /* Text Colors */
  --color-text: var(--vt-c-text-primary);
  --color-text-secondary: var(--vt-c-text-secondary);
  --color-text-tertiary: var(--vt-c-text-tertiary);
  --color-heading: var(--vt-c-text-primary);

  /* Primary Colors */
  --color-primary: var(--vt-c-primary-600);
  --color-primary-hover: var(--vt-c-primary-700);
  --color-primary-light: var(--vt-c-primary-50);

  /* Status Colors */
  --color-success: var(--vt-c-success-600);
  --color-success-light: var(--vt-c-success-50);
  --color-warning: var(--vt-c-warning-600);
  --color-warning-light: var(--vt-c-warning-50);
  --color-error: var(--vt-c-error-600);
  --color-error-light: var(--vt-c-error-50);

  /* Shadow Colors */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Layout */
  --section-gap: 160px;
  --border-radius: 8px;
  --border-radius-sm: 4px;
  --border-radius-lg: 12px;
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  font-weight: normal;
}

body {
  min-height: 100vh;
  color: var(--color-text);
  background: var(--color-background-soft);
  transition:
    color 0.3s ease,
    background-color 0.3s ease;
  line-height: 1.6;
  font-family:
    'Inter',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    'Fira Sans',
    'Droid Sans',
    'Helvetica Neue',
    sans-serif;
  font-size: 14px;
  font-weight: 400;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  padding: 0;
}

/* Global utility classes */
.text-primary { color: var(--color-text); }
.text-secondary { color: var(--color-text-secondary); }
.text-tertiary { color: var(--color-text-tertiary); }
.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-error { color: var(--color-error); }

.bg-surface { background-color: var(--color-surface); }
.bg-elevated { background-color: var(--color-background-elevated); }

.border-radius { border-radius: var(--border-radius); }
.border-radius-sm { border-radius: var(--border-radius-sm); }
.border-radius-lg { border-radius: var(--border-radius-lg); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow { box-shadow: var(--shadow); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
