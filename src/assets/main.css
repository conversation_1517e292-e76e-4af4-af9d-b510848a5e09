@import './base.css';

#app {
  font-weight: normal;
  width: 100%;
  min-height: 100vh;
  background-color: var(--color-background-soft);
}

/* Link styles */
a {
  text-decoration: none;
  color: var(--color-primary);
  transition: all 0.3s ease;
  border-radius: var(--border-radius-sm);
}

a:hover {
  color: var(--color-primary-hover);
  background-color: var(--color-primary-light);
}

a:focus {
  outline: 2px solid var(--color-border-focus);
  outline-offset: 2px;
}

/* Modern scrollbar styles */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--color-background-mute);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--color-border-hover);
  border-radius: 3px;
  transition: background-color 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-tertiary);
}

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid var(--color-border-focus);
  outline-offset: 2px;
}

button:focus,
input:focus,
select:focus,
textarea:focus {
  outline: 2px solid var(--color-border-focus);
  outline-offset: 2px;
}
