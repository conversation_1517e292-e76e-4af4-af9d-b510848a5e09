import type { EChartsOption } from 'echarts'
// chartOptions 工具函数负责:
// 提供各类图表的配置模板
// 数据格式转换

interface LineChartConfig {
  smooth?: boolean
  lineColor?: string
  areaColor?: string
  maxPoints?: number // 添加最大显示点数配置
}

interface LineChartData {
  xAxis: string[]
  series: number[]
  config?: LineChartConfig
}

export const createLineChart = (data: LineChartData): EChartsOption => {
  // 获取最新的N条数据
  const maxPoints = data.config?.maxPoints || 20
  const startIndex = Math.max(0, data.xAxis.length - maxPoints)
  const displayXAxis = data.xAxis.slice(startIndex)
  const displaySeries = data.series.slice(startIndex)

  return {
    backgroundColor: 'transparent',
    textStyle: {
      color: '#374151'
    },
    xAxis: {
      show: true,
      type: 'category',
      boundaryGap: false,
      data: displayXAxis,
      axisLabel: {
        interval: 'auto',
        rotate: 45,
        color: '#6B7280',
        fontSize: 12
      },
      axisLine: {
        lineStyle: {
          color: '#E5E7EB'
        }
      },
      axisTick: {
        lineStyle: {
          color: '#E5E7EB'
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: '#6B7280',
        fontSize: 12
      },
      axisLine: {
        lineStyle: {
          color: '#E5E7EB'
        }
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#F3F4F6'
        }
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      top: '5%',
      bottom: '15%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#E5E7EB',
      borderWidth: 1,
      textStyle: {
        color: '#374151'
      },
      formatter: (params: any) => {
        const param = params[0]
        return `时间：${param.axisValue}<br/>温度：${param.value}°C`
      }
    },
    series: [{
      type: 'line',
      data: displaySeries,
      smooth: data.config?.smooth ?? true,
      showSymbol: false,
      lineStyle: {
        width: 3,
        color: data.config?.lineColor || '#3B82F6'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0,
            color: data.config?.areaColor || 'rgba(59, 130, 246, 0.3)'
          }, {
            offset: 1,
            color: 'rgba(59, 130, 246, 0.05)'
          }]
        }
      }
    }]
  }
}

export const createBarChart = (config: {
  series: Array<number>
  xAxis: {
    type: string
    data: string[]
    axisLabel?: any
  }
  maxPoints?: number
}): EChartsOption => {
  const maxPoints = config.maxPoints || 20
  const data = Array.isArray(config.series) ? config.series : []
  const startIndex = Math.max(0, data.length - maxPoints)
  const displayData = data.slice(startIndex)
  const displayXAxis = config.xAxis.data.slice(startIndex)

  // 计算数据范围
  const min = Math.min(...displayData)
  const max = Math.max(...displayData)
  const range = max - min

  return {
    backgroundColor: 'transparent',
    textStyle: {
      color: '#374151'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
        shadowStyle: {
          color: 'rgba(59, 130, 246, 0.1)'
        }
      },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#E5E7EB',
      borderWidth: 1,
      textStyle: {
        color: '#374151'
      },
      formatter: (params: any) => {
        const param = params[0]
        return `时间：${param.axisValue}<br/>请求量：${param.value}`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '5%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: displayXAxis,
      axisLabel: {
        color: '#6B7280',
        interval: 0,
        rotate: 45,
        fontSize: 12,
        ...config.xAxis?.axisLabel
      },
      axisLine: {
        lineStyle: {
          color: '#E5E7EB'
        }
      },
      axisTick: {
        lineStyle: {
          color: '#E5E7EB'
        }
      }
    },
    yAxis: {
      type: 'value',
      min: Math.floor(min - range * 0.1),
      max: Math.ceil(max + range * 0.1),
      splitNumber: 5,
      axisLabel: {
        color: '#6B7280',
        fontSize: 12
      },
      axisLine: {
        lineStyle: {
          color: '#E5E7EB'
        }
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#F3F4F6'
        }
      }
    },
    series: [{
      type: 'bar',
      data: displayData,
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0,
            color: '#60A5FA'
          }, {
            offset: 1,
            color: '#3B82F6'
          }]
        },
        borderRadius: [4, 4, 0, 0]
      },
      barWidth: '60%',
      emphasis: {
        itemStyle: {
          color: '#2563EB',
          shadowBlur: 10,
          shadowColor: 'rgba(37, 99, 235, 0.3)'
        }
      }
    }]
  }
}

export const createPieChart = (config: {
  series: Array<{ name: string; value: number }>
}): EChartsOption => ({
  backgroundColor: 'transparent',
  textStyle: {
    color: '#374151'
  },
  tooltip: {
    trigger: 'item',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderColor: '#E5E7EB',
    borderWidth: 1,
    textStyle: {
      color: '#374151'
    },
    formatter: '{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left',
    textStyle: {
      color: '#6B7280',
      fontSize: 12
    }
  },
  series: [{
    type: 'pie',
    radius: ['40%', '70%'],
    center: ['60%', '50%'],
    data: config.series,
    emphasis: {
      itemStyle: {
        shadowBlur: 10,
        shadowOffsetX: 0,
        shadowColor: 'rgba(59, 130, 246, 0.3)'
      }
    },
    label: {
      color: '#374151',
      fontSize: 12,
      fontWeight: 500
    },
    labelLine: {
      lineStyle: {
        color: '#D1D5DB'
      }
    },
    itemStyle: {
      borderRadius: 4,
      borderColor: '#fff',
      borderWidth: 2
    }
  }]
})

export const createMapChart = (data: any): EChartsOption => {
  return {
    backgroundColor: 'transparent',
    textStyle: {
      color: '#374151'
    },
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#E5E7EB',
      borderWidth: 1,
      textStyle: {
        color: '#374151'
      },
      formatter: '{b}: {c} 台设备'
    },
    visualMap: {
      min: 0,
      max: 100,
      text: ['高', '低'],
      realtime: false,
      calculable: true,
      textStyle: {
        color: '#6B7280'
      },
      inRange: {
        color: ['#EFF6FF', '#3B82F6']
      },
      itemWidth: 20,
      itemHeight: 140,
      borderColor: '#E5E7EB',
      borderWidth: 1,
      backgroundColor: 'rgba(255, 255, 255, 0.9)'
    },
    series: [
      {
        name: '设备分布',
        type: 'map',
        map: 'china',
        label: {
          show: true,
          fontSize: 11,
          distance: 5,
          color: '#374151',
          fontWeight: 500
        },
        itemStyle: {
          borderColor: '#E5E7EB',
          borderWidth: 1,
          areaColor: '#F9FAFB'
        },
        emphasis: {
          label: {
            color: '#1F2937',
            fontSize: 12,
            fontWeight: 600
          },
          itemStyle: {
            areaColor: '#DBEAFE',
            borderColor: '#3B82F6',
            borderWidth: 2,
            shadowBlur: 10,
            shadowColor: 'rgba(59, 130, 246, 0.3)'
          }
        },
        zoom: 1.2,
        roam: true,
        layoutCenter: ['50%', '50%'],
        data: data.series
      }
    ]
  }
}
